<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天助手 - RAGFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-blue-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">仪表板</a>
                    <a href="datasets.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">数据集</a>
                    <a href="assistants.html" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">聊天助手</a>
                    <a href="agents.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">代理</a>
                    <a href="api.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">API</a>
                    <a href="settings.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">设置</a>
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-3">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <span class="text-sm font-medium text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 页面标题和操作 -->
        <div class="px-4 py-6 sm:px-0">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">聊天助手</h1>
                    <p class="mt-2 text-gray-600">创建和管理您的AI聊天助手，基于知识库提供智能对话服务</p>
                </div>
                <button onclick="openCreateModal()" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建助手
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索助手..." class="input-modern pl-10 w-full">
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <select class="input-modern">
                            <option>所有状态</option>
                            <option>在线</option>
                            <option>离线</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 助手列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 助手卡片 1 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">产品顾问</h3>
                                <p class="text-sm text-gray-500">基于产品知识库</p>
                            </div>
                        </div>
                        <span class="status-indicator status-online"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">关联数据集</span>
                            <span class="text-gray-900">产品知识库</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">156</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-15</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openChat('product-advisor')" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                            <i class="fas fa-comments mr-2"></i>开始对话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 助手卡片 2 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">技术支持</h3>
                                <p class="text-sm text-gray-500">基于技术文档库</p>
                            </div>
                        </div>
                        <span class="status-indicator status-online"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">关联数据集</span>
                            <span class="text-gray-900">技术文档库</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">89</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-10</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openChat('tech-support')" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                            <i class="fas fa-comments mr-2"></i>开始对话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 助手卡片 3 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">客户服务</h3>
                                <p class="text-sm text-gray-500">基于客户支持库</p>
                            </div>
                        </div>
                        <span class="status-indicator status-offline"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">关联数据集</span>
                            <span class="text-gray-900">客户支持库</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">234</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-08</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openChat('customer-service')" class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700">
                            <i class="fas fa-comments mr-2"></i>开始对话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建助手模态框 -->
    <div id="createModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">创建聊天助手</h3>
                <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">助手名称</label>
                        <input type="text" class="input-modern w-full" placeholder="输入助手名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea class="input-modern w-full" rows="3" placeholder="描述这个助手的用途"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">关联数据集</label>
                        <select class="input-modern w-full">
                            <option>选择数据集</option>
                            <option>产品知识库</option>
                            <option>技术文档库</option>
                            <option>客户支持库</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">LLM模型</label>
                        <select class="input-modern w-full">
                            <option>GPT-4</option>
                            <option>GPT-3.5-turbo</option>
                            <option>Claude-3</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">系统提示词</label>
                        <textarea class="input-modern w-full" rows="4" placeholder="输入系统提示词，指导助手的行为和回答方式"></textarea>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeCreateModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="btn-primary">
                        创建助手
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 聊天界面模态框 -->
    <div id="chatModal" class="modal-overlay hidden">
        <div class="modal-content" style="max-width: 800px; height: 80vh;">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">与助手对话</h3>
                <button onclick="closeChatModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex flex-col h-full">
                <!-- 聊天消息区域 -->
                <div id="chatMessages" class="flex-1 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
                    <div class="message-assistant">
                        <p>您好！我是您的AI助手，有什么可以帮助您的吗？</p>
                    </div>
                </div>
                <!-- 输入区域 -->
                <div class="flex space-x-2">
                    <input type="text" id="messageInput" placeholder="输入您的问题..." class="input-modern flex-1">
                    <button onclick="sendMessage()" class="btn-primary">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.remove('hidden');
        }

        function closeCreateModal() {
            document.getElementById('createModal').classList.add('hidden');
        }

        function openChat(assistantId) {
            document.getElementById('chatModal').classList.remove('hidden');
            // 这里可以根据assistantId加载对应的助手配置
        }

        function closeChatModal() {
            document.getElementById('chatModal').classList.add('hidden');
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            if (!message) return;

            const messagesContainer = document.getElementById('chatMessages');
            
            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'message-user';
            userMessage.innerHTML = `<p>${message}</p>`;
            messagesContainer.appendChild(userMessage);

            // 清空输入框
            input.value = '';

            // 模拟助手回复
            setTimeout(() => {
                const assistantMessage = document.createElement('div');
                assistantMessage.className = 'message-assistant';
                assistantMessage.innerHTML = `<p>我理解您的问题。让我为您提供相关信息...</p>`;
                messagesContainer.appendChild(assistantMessage);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 1000);

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 回车发送消息
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.getElementById('messageInput');
            if (messageInput) {
                messageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });
    </script>
</body>
</html> 