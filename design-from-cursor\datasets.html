<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据集管理 - RAGFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-blue-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">仪表板</a>
                    <a href="datasets.html" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">数据集</a>
                    <a href="assistants.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">聊天助手</a>
                    <a href="agents.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">代理</a>
                    <a href="api.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">API</a>
                    <a href="settings.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">设置</a>
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-3">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <span class="text-sm font-medium text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 页面标题和操作 -->
        <div class="px-4 py-6 sm:px-0">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">数据集管理</h1>
                    <p class="mt-2 text-gray-600">管理您的知识库数据集，上传文档并构建智能检索系统</p>
                </div>
                <button onclick="openCreateModal()" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建数据集
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索数据集..." class="input-modern pl-10 w-full">
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <select class="input-modern">
                            <option>所有状态</option>
                            <option>已解析</option>
                            <option>解析中</option>
                            <option>解析失败</option>
                        </select>
                        <select class="input-modern">
                            <option>所有权限</option>
                            <option>仅我</option>
                            <option>团队</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据集列表 -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">数据集列表</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="table-modern">
                    <thead>
                        <tr>
                            <th>数据集名称</th>
                            <th>文档数量</th>
                            <th>块数量</th>
                            <th>嵌入模型</th>
                            <th>权限</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="flex items-center">
                                    <i class="fas fa-database text-blue-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">产品知识库</div>
                                        <div class="text-sm text-gray-500">包含产品文档和FAQ</div>
                                    </div>
                                </div>
                            </td>
                            <td>24</td>
                            <td>1,234</td>
                            <td>BAAI/bge-large-zh-v1.5</td>
                            <td><span class="tag tag-blue">仅我</span></td>
                            <td><span class="tag tag-green">已解析</span></td>
                            <td>2024-01-15</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800" onclick="openEditModal()">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="flex items-center">
                                    <i class="fas fa-database text-green-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">技术文档库</div>
                                        <div class="text-sm text-gray-500">API文档和技术规范</div>
                                    </div>
                                </div>
                            </td>
                            <td>18</td>
                            <td>856</td>
                            <td>BAAI/bge-large-zh-v1.5</td>
                            <td><span class="tag tag-purple">团队</span></td>
                            <td><span class="tag tag-green">已解析</span></td>
                            <td>2024-01-10</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div class="flex items-center">
                                    <i class="fas fa-database text-orange-600 mr-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-900">客户支持库</div>
                                        <div class="text-sm text-gray-500">常见问题和解决方案</div>
                                    </div>
                                </div>
                            </td>
                            <td>32</td>
                            <td>2,156</td>
                            <td>BAAI/bge-large-zh-v1.5</td>
                            <td><span class="tag tag-blue">仅我</span></td>
                            <td><span class="tag tag-orange">解析中</span></td>
                            <td>2024-01-20</td>
                            <td>
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页 -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条结果
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">上一页</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">下一页</a>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建数据集模态框 -->
    <div id="createModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">创建数据集</h3>
                <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">数据集名称</label>
                        <input type="text" class="input-modern w-full" placeholder="输入数据集名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea class="input-modern w-full" rows="3" placeholder="描述这个数据集的用途"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">嵌入模型</label>
                        <select class="input-modern w-full">
                            <option>BAAI/bge-large-zh-v1.5</option>
                            <option>BAAI/bge-zh-v1.5</option>
                            <option>text-embedding-ada-002</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">权限设置</label>
                        <select class="input-modern w-full">
                            <option>仅我</option>
                            <option>团队</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">分块方法</label>
                        <select class="input-modern w-full">
                            <option>通用 (naive)</option>
                            <option>手动 (manual)</option>
                            <option>问答 (qa)</option>
                            <option>表格 (table)</option>
                            <option>论文 (paper)</option>
                            <option>书籍 (book)</option>
                            <option>法律 (laws)</option>
                            <option>演示文稿 (presentation)</option>
                            <option>图片 (picture)</option>
                            <option>单一 (one)</option>
                            <option>邮件 (email)</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeCreateModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="btn-primary">
                        创建数据集
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.remove('hidden');
        }

        function closeCreateModal() {
            document.getElementById('createModal').classList.add('hidden');
        }

        function openEditModal() {
            // 编辑模态框逻辑
            alert('编辑功能开发中...');
        }
    </script>
</body>
</html> 