<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - 个人设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="profile.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-user mr-2"></i>个人设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">个人设置</h1>
            <p class="mt-2 text-gray-600">管理您的账户信息和偏好</p>
        </div>

        <div class="bg-white rounded-lg shadow">
            <div class="p-6">
                <div class="space-y-8">
                    <!-- Profile Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">个人信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                                <input type="text" value="张三" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">职位</label>
                                <input type="text" value="产品经理" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">部门</label>
                                <input type="text" value="技术部" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                        </div>
                    </div>

                    <!-- Avatar -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">头像</h3>
                        <div class="flex items-center space-x-4">
                            <img src="https://via.placeholder.com/80x80/667eea/ffffff?text=ZS" alt="Avatar" class="w-20 h-20 rounded-full">
                            <div>
                                <button class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                                    <i class="fas fa-upload mr-2"></i>上传头像
                                </button>
                                <p class="text-sm text-gray-600 mt-1">支持 JPG, PNG, GIF 格式，最大 2MB</p>
                            </div>
                        </div>
                    </div>

                    <!-- Password -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">密码安全</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                                <input type="password" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                                <input type="password" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                                <input type="password" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            </div>
                        </div>
                    </div>

                    <!-- Preferences -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">通知偏好</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" checked class="mr-3">
                                <label class="text-sm">邮件通知</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" checked class="mr-3">
                                <label class="text-sm">系统消息</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="mr-3">
                                <label class="text-sm">营销邮件</label>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button -->
                    <div class="pt-4 border-t">
                        <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                            <i class="fas fa-save mr-2"></i>保存更改
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>