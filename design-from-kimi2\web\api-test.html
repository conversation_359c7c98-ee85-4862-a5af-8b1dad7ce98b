<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="api-test.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-code mr-2"></i>API测试
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">API测试工具</h1>
            <p class="mt-2 text-gray-600">测试RAGFlow API接口</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Request Panel -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">请求配置</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API端点</label>
                        <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            <option value="/api/v1/datasets">创建数据集</option>
                            <option value="/api/v1/chat/completions">创建聊天完成</option>
                            <option value="/api/v1/agents">创建代理</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">请求方法</label>
                        <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                            <option>POST</option>
                            <option>GET</option>
                            <option>PUT</option>
                            <option>DELETE</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Authorization</label>
                        <input type="text" placeholder="Bearer your-api-key" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">请求体</label>
                        <textarea class="w-full h-32 px-3 py-2 border rounded-lg font-mono text-sm focus:ring-2 focus:ring-indigo-500" placeholder='{
  "name": "测试数据集",
  "description": "用于API测试"
}'></textarea>
                    </div>

                    <button class="w-full bg-indigo-600 text-white py-2 rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-paper-plane mr-2"></i>发送请求
                    </button>
                </div>
            </div>

            <!-- Response Panel -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">响应结果</h3>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">状态码</label>
                        <div class="px-3 py-2 bg-green-100 text-green-800 rounded-lg font-mono">200 OK</div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">响应时间</label>
                        <div class="px-3 py-2 bg-gray-100 rounded-lg font-mono">245ms</div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">响应体</label>
                        <div class="code-block rounded-lg p-3 text-sm overflow-auto">
<pre>{
  "code": 0,
  "message": "success",
  "data": {
    "id": "ds_123456",
    "name": "测试数据集",
    "description": "用于API测试",
    "create_time": "2024-01-15T10:30:00Z"
  }
}</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Documentation -->
        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">API文档</h3>
            <div class="space-y-4">
                <div class="border rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-2">创建数据集</h4>
                    <p class="text-sm text-gray-600 mb-2">POST /api/v1/datasets</p>
                    <div class="code-block rounded p-2 text-sm">
                        <pre>curl -X POST "https://api.ragflow.com/v1/datasets" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "我的数据集",
    "description": "数据集描述"
  }'</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>