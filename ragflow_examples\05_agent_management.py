#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 代理管理示例
演示如何创建、更新、删除、列出智能代理，支持DSL配置
"""
from ragflow_sdk import RAGFlow
from typing import Dict

class AgentManager:
    def __init__(self, api_key: str, base_url: str):
        """初始化RAGFlow客户端"""
        self.rag_object = RAGFlow(api_key=api_key, base_url=base_url)

    def create_agent_example(self, title: str, dsl: Dict, description: str = None):
        """创建代理示例"""
        print("=== 创建代理示例 ===")
        try:
            self.rag_object.create_agent(
                title=title,
                dsl=dsl,
                description=description or "示例代理"
            )
            print(f"创建代理成功: {title}")
        except Exception as e:
            print(f"创建代理失败: {e}")

    def list_agents_example(self):
        """列出代理示例"""
        print("=== 列出代理示例 ===")
        try:
            agents = self.rag_object.list_agents()
            for agent in agents:
                print(f"  - {agent.title} (ID: {agent.id})")
            return agents
        except Exception as e:
            print(f"列出代理失败: {e}")
            return []

    def update_agent_example(self, agent_id: str, new_title: str):
        """更新代理示例"""
        print("=== 更新代理示例 ===")
        try:
            self.rag_object.update_agent(agent_id=agent_id, title=new_title)
            print(f"代理(ID: {agent_id}) 已重命名为: {new_title}")
        except Exception as e:
            print(f"更新代理失败: {e}")

    def delete_agent_example(self, agent_id: str):
        """删除代理示例"""
        print("=== 删除代理示例 ===")
        try:
            self.rag_object.delete_agent(agent_id=agent_id)
            print(f"代理(ID: {agent_id}) 已删除")
        except Exception as e:
            print(f"删除代理失败: {e}")


def main():
    """主函数 - 演示代理管理的完整流程"""
    API_KEY = "ragflow-ZjMDYyNDQwNjIxNzExZjBhMjgyMDI0Mm"  # 例如："abc123def456"
    BASE_URL = "http://localhost:9380"  # 例如："http://localhost:9380"
    agent_title = "测试代理"
    agent_dsl = {
        # 这里填写你的DSL配置
        "nodes": [],
        "edges": []
    }

    manager = AgentManager(API_KEY, BASE_URL)
    # 创建代理
    manager.create_agent_example(agent_title, agent_dsl)
    # 列出代理
    agents = manager.list_agents_example()
    # 更新代理
    if agents:
        manager.update_agent_example(agents[0].id, "新代理名")
    # 删除代理
    if agents:
        manager.delete_agent_example(agents[0].id)

if __name__ == "__main__":
    main() 