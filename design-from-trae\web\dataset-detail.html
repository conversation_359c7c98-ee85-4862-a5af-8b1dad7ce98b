<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - 数据集详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="datasets.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-database mr-2"></i>数据集
                        </a>
                        <a href="chat-assistants.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-robot mr-2"></i>聊天助手
                        </a>
                        <a href="agents.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-magic mr-2"></i>代理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <div class="flex items-center space-x-2 mb-2">
                        <a href="datasets.html" class="text-indigo-600 hover:text-indigo-500">
                            <i class="fas fa-arrow-left mr-1"></i>返回数据集
                        </a>
                    </div>
                    <h1 class="text-3xl font-bold text-gray-900">技术文档</h1>
                    <p class="mt-2 text-gray-600">产品技术文档和API参考集合</p>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-play mr-2"></i>开始解析
                    </button>
                    <button class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-upload mr-2"></i>上传文档
                    </button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                        <i class="fas fa-edit mr-2"></i>编辑
                    </button>
                </div>
            </div>
        </div>

        <!-- Dataset Info -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <!-- Documents List -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">文档列表</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-center justify-between p-4 border rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-pdf text-red-500 text-xl mr-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-900">API_Reference_v2.1.pdf</p>
                                        <p class="text-sm text-gray-600">2.4 MB • 上传于 2024-01-15</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已解析</span>
                                    <button class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 border rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-word text-blue-500 text-xl mr-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-900">用户手册.docx</p>
                                        <p class="text-sm text-gray-600">1.8 MB • 上传于 2024-01-14</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">处理中</span>
                                    <button class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 border rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-file-alt text-gray-500 text-xl mr-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-900">开发指南.md</p>
                                        <p class="text-sm text-gray-600">856 KB • 上传于 2024-01-13</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已解析</span>
                                    <button class="text-indigo-600 hover:text-indigo-800">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-6">
                <!-- Dataset Stats -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">数据集统计</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">文档总数</span>
                            <span class="font-semibold">45</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">已解析</span>
                            <span class="font-semibold text-green-600">38</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">处理中</span>
                            <span class="font-semibold text-yellow-600">5</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">失败</span>
                            <span class="font-semibold text-red-600">2</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">总大小</span>
                            <span class="font-semibold">128 MB</span>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">配置信息</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">数据集ID</label>
                            <input type="text" value="ds_7f8a9b2c" readonly class="w-full px-3 py-2 bg-gray-50 border rounded-md text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                            <input type="text" value="2024-01-10 14:30:00" readonly class="w-full px-3 py-2 bg-gray-50 border rounded-md text-sm">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">解析语言</label>
                            <select class="w-full px-3 py-2 border rounded-md text-sm">
                                <option>中文</option>
                                <option>英文</option>
                                <option>中英文混合</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>