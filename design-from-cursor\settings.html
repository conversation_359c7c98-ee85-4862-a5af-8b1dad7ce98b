<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - RAGFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-blue-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">仪表板</a>
                    <a href="datasets.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">数据集</a>
                    <a href="assistants.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">聊天助手</a>
                    <a href="agents.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">代理</a>
                    <a href="api.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">API</a>
                    <a href="settings.html" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">设置</a>
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-3">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <span class="text-sm font-medium text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="px-4 py-6 sm:px-0">
            <h1 class="text-3xl font-bold text-gray-900">系统设置</h1>
            <p class="mt-2 text-gray-600">管理您的账户设置、系统配置和安全选项</p>
        </div>

        <!-- 设置选项卡 -->
        <div class="bg-white shadow rounded-lg">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button onclick="showTab('profile')" class="tab-button active py-4 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                        <i class="fas fa-user mr-2"></i>个人资料
                    </button>
                    <button onclick="showTab('security')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        <i class="fas fa-shield-alt mr-2"></i>安全设置
                    </button>
                    <button onclick="showTab('system')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        <i class="fas fa-cog mr-2"></i>系统配置
                    </button>
                    <button onclick="showTab('notifications')" class="tab-button py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        <i class="fas fa-bell mr-2"></i>通知设置
                    </button>
                </nav>
            </div>

            <!-- 个人资料设置 -->
            <div id="profile" class="tab-content p-6">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">个人信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                                <input type="text" value="admin" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱</label>
                                <input type="email" value="<EMAIL>" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">显示名称</label>
                                <input type="text" value="系统管理员" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">手机号</label>
                                <input type="tel" value="+86 138 0000 0000" class="input-modern w-full">
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">头像设置</h3>
                        <div class="flex items-center space-x-6">
                            <img class="h-20 w-20 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <div>
                                <button class="btn-primary">
                                    <i class="fas fa-upload mr-2"></i>上传新头像
                                </button>
                                <p class="text-sm text-gray-500 mt-2">支持 JPG、PNG 格式，最大 2MB</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button class="btn-primary">
                            <i class="fas fa-save mr-2"></i>保存更改
                        </button>
                    </div>
                </div>
            </div>

            <!-- 安全设置 -->
            <div id="security" class="tab-content p-6 hidden">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">密码设置</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
                                <input type="password" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
                                <input type="password" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
                                <input type="password" class="input-modern w-full">
                            </div>
                            <button class="btn-primary">
                                <i class="fas fa-key mr-2"></i>更改密码
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">双因素认证</h3>
                        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <h4 class="font-medium text-gray-900">Google Authenticator</h4>
                                <p class="text-sm text-gray-500">使用Google Authenticator应用进行双因素认证</p>
                            </div>
                            <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                启用
                            </button>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">登录历史</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Windows 10 - Chrome</p>
                                        <p class="text-xs text-gray-500">192.168.1.100 - 2024-01-20 14:30</p>
                                    </div>
                                    <span class="tag tag-green">当前会话</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">MacOS - Safari</p>
                                        <p class="text-xs text-gray-500">192.168.1.101 - 2024-01-19 09:15</p>
                                    </div>
                                    <span class="tag tag-gray">已结束</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统配置 -->
            <div id="system" class="tab-content p-6 hidden">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">默认设置</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">默认嵌入模型</label>
                                <select class="input-modern w-full">
                                    <option>BAAI/bge-large-zh-v1.5</option>
                                    <option>BAAI/bge-zh-v1.5</option>
                                    <option>text-embedding-ada-002</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">默认LLM模型</label>
                                <select class="input-modern w-full">
                                    <option>GPT-4</option>
                                    <option>GPT-3.5-turbo</option>
                                    <option>Claude-3</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">默认分块方法</label>
                                <select class="input-modern w-full">
                                    <option>通用 (naive)</option>
                                    <option>手动 (manual)</option>
                                    <option>问答 (qa)</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">默认权限</label>
                                <select class="input-modern w-full">
                                    <option>仅我</option>
                                    <option>团队</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">系统限制</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">最大数据集数量</label>
                                <input type="number" value="100" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">最大文档大小 (MB)</label>
                                <input type="number" value="50" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API调用限制 (次/分钟)</label>
                                <input type="number" value="1000" class="input-modern w-full">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">会话超时时间 (分钟)</label>
                                <input type="number" value="30" class="input-modern w-full">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button class="btn-primary">
                            <i class="fas fa-save mr-2"></i>保存配置
                        </button>
                    </div>
                </div>
            </div>

            <!-- 通知设置 -->
            <div id="notifications" class="tab-content p-6 hidden">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">邮件通知</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">系统通知</h4>
                                    <p class="text-sm text-gray-500">接收系统维护和更新通知</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">文档处理完成</h4>
                                    <p class="text-sm text-gray-500">文档解析完成时发送通知</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">API使用统计</h4>
                                    <p class="text-sm text-gray-500">每周发送API使用统计报告</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-4">浏览器通知</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-900">实时通知</h4>
                                    <p class="text-sm text-gray-500">在浏览器中显示实时通知</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button class="btn-primary">
                            <i class="fas fa-save mr-2"></i>保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有选项卡内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.add('hidden');
            });

            // 移除所有选项卡按钮的active类
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active', 'border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // 显示选中的选项卡内容
            document.getElementById(tabName).classList.remove('hidden');

            // 激活选中的选项卡按钮
            event.target.classList.add('active', 'border-blue-500', 'text-blue-600');
            event.target.classList.remove('border-transparent', 'text-gray-500');
        }
    </script>
</body>
</html> 