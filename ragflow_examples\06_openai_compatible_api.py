#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow OpenAI兼容API示例
演示如何通过OpenAI风格API进行流式和非流式对话
"""
from openai import OpenAI

API_KEY = "ragflow-ZjMDYyNDQwNjIxNzExZjBhMjgyMDI0Mm"  # 例如："abc123def456"
BASE_URL = "http://localhost:9380/api/v1/chats_openai/your_chat_id"  # 例如："http://localhost:9380/api/v1/chats_openai/your_chat_id"


def chat_completion_example(stream: bool = False):
    """OpenAI兼容API对话示例"""
    print(f"=== OpenAI兼容API对话示例（stream={stream}） ===")
    client = OpenAI(api_key=API_KEY, base_url=BASE_URL)
    completion = client.chat.completions.create(
        model="model",
        messages=[
            {"role": "system", "content": "你是一个乐于助人的助手。"},
            {"role": "user", "content": "你是谁？"},
        ],
        stream=stream
    )
    if stream:
        for chunk in completion:
            print(chunk)
    else:
        print(completion.choices[0].message.content)


def main():
    """主函数 - 演示OpenAI兼容API的流式和非流式对话"""
    # 非流式对话
    chat_completion_example(stream=False)
    # 流式对话
    chat_completion_example(stream=True)

if __name__ == "__main__":
    main() 