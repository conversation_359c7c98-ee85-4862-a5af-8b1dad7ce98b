<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - 系统设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="settings.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-cog mr-2"></i>系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">系统设置</h1>
            <p class="mt-2 text-gray-600">配置系统参数和集成选项</p>
        </div>

        <!-- Settings Tabs -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b">
                <div class="flex space-x-8 px-6">
                    <button class="py-4 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium">通用设置</button>
                    <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">API配置</button>
                    <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">通知设置</button>
                    <button class="py-4 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700">安全设置</button>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- General Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">通用设置</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">系统名称</label>
                            <input type="text" value="RAGFlow企业版" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">默认语言</label>
                            <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option>简体中文</option>
                                <option>English</option>
                                <option>日本語</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">时区</label>
                            <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option>Asia/Shanghai (UTC+8)</option>
                                <option>America/New_York (UTC-5)</option>
                                <option>Europe/London (UTC+0)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- API Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">API配置</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                            <div class="flex space-x-2">
                                <input type="password" value="sk-1234567890abcdef" class="flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500" readonly>
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">重新生成</button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">速率限制</label>
                            <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option>100 请求/分钟</option>
                                <option>500 请求/分钟</option>
                                <option>1000 请求/分钟</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="pt-4 border-t">
                    <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-save mr-2"></i>保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>