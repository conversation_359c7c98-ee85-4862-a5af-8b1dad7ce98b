<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理 - RAGFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-blue-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">仪表板</a>
                    <a href="datasets.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">数据集</a>
                    <a href="assistants.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">聊天助手</a>
                    <a href="agents.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">代理</a>
                    <a href="api.html" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">API</a>
                    <a href="settings.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">设置</a>
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-3">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <span class="text-sm font-medium text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="px-4 py-6 sm:px-0">
            <h1 class="text-3xl font-bold text-gray-900">API管理</h1>
            <p class="mt-2 text-gray-600">管理API密钥、查看文档和测试接口</p>
        </div>

        <!-- API概览 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-key text-2xl text-blue-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">API密钥</dt>
                                <dd class="text-lg font-medium text-gray-900">已配置</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-chart-line text-2xl text-green-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">今日调用</dt>
                                <dd class="text-lg font-medium text-gray-900">1,234</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-clock text-2xl text-purple-600"></i>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">平均响应时间</dt>
                                <dd class="text-lg font-medium text-gray-900">245ms</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API配置和测试 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- API配置 -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">API配置</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API密钥</label>
                            <div class="flex">
                                <input type="password" value="ragflow-api-key-xxxxx" class="input-modern flex-1" readonly>
                                <button class="ml-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="ml-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">基础URL</label>
                            <input type="text" value="http://localhost:9380" class="input-modern w-full">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">超时设置</label>
                            <input type="number" value="30" class="input-modern w-full" placeholder="秒">
                        </div>
                        <div class="flex space-x-2">
                            <button class="btn-primary">
                                <i class="fas fa-save mr-2"></i>保存配置
                            </button>
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-refresh mr-2"></i>重新生成密钥
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API测试工具 -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">API测试工具</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择接口</label>
                            <select class="input-modern w-full">
                                <option>创建数据集</option>
                                <option>上传文档</option>
                                <option>创建聊天助手</option>
                                <option>发送消息</option>
                                <option>检索块</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">请求参数</label>
                            <textarea class="input-modern w-full" rows="6" placeholder="输入JSON格式的请求参数"></textarea>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="testAPI()" class="btn-primary">
                                <i class="fas fa-play mr-2"></i>发送请求
                            </button>
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                                <i class="fas fa-file-code mr-2"></i>生成代码
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API文档 -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">API文档</h3>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <!-- 数据集管理 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">数据集管理</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">POST /api/v1/datasets</span>
                                <span class="tag tag-green">创建数据集</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">创建新的数据集</p>
                            <div class="bg-gray-100 rounded p-3">
                                <pre class="text-xs text-gray-800"><code>{
  "name": "数据集名称",
  "description": "数据集描述",
  "embedding_model": "BAAI/bge-large-zh-v1.5",
  "permission": "me"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- 文档管理 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">文档管理</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">POST /api/v1/datasets/{id}/documents</span>
                                <span class="tag tag-blue">上传文档</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">上传文档到指定数据集</p>
                            <div class="bg-gray-100 rounded p-3">
                                <pre class="text-xs text-gray-800"><code>{
  "display_name": "文档名称",
  "blob": "base64编码的文档内容"
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- 聊天助手 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">聊天助手</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">POST /api/v1/chats</span>
                                <span class="tag tag-purple">创建助手</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">创建新的聊天助手</p>
                            <div class="bg-gray-100 rounded p-3">
                                <pre class="text-xs text-gray-800"><code>{
  "name": "助手名称",
  "dataset_ids": ["dataset_id"],
  "llm": {
    "model_name": "gpt-4",
    "temperature": 0.1
  }
}</code></pre>
                            </div>
                        </div>
                    </div>

                    <!-- OpenAI兼容API -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">OpenAI兼容API</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-gray-700">POST /api/v1/chats_openai/{chat_id}</span>
                                <span class="tag tag-orange">聊天完成</span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">使用OpenAI兼容的接口进行对话</p>
                            <div class="bg-gray-100 rounded p-3">
                                <pre class="text-xs text-gray-800"><code>{
  "model": "gpt-4",
  "messages": [
    {"role": "user", "content": "你好"}
  ],
  "stream": false
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 使用统计 -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">使用统计</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">1,234</div>
                        <div class="text-sm text-gray-500">今日调用</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">98.5%</div>
                        <div class="text-sm text-gray-500">成功率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">245ms</div>
                        <div class="text-sm text-gray-500">平均响应时间</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">5.2GB</div>
                        <div class="text-sm text-gray-500">数据传输量</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testAPI() {
            // 模拟API测试
            const testButton = event.target;
            const originalText = testButton.innerHTML;
            
            testButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>测试中...';
            testButton.disabled = true;
            
            setTimeout(() => {
                testButton.innerHTML = originalText;
                testButton.disabled = false;
                alert('API测试完成！响应时间: 245ms');
            }, 2000);
        }

        // 复制API密钥
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.querySelector('.fa-copy').parentElement;
            copyButton.addEventListener('click', function() {
                const apiKey = document.querySelector('input[type="password"]').value;
                navigator.clipboard.writeText(apiKey).then(() => {
                    alert('API密钥已复制到剪贴板');
                });
            });
        });
    </script>
</body>
</html> 