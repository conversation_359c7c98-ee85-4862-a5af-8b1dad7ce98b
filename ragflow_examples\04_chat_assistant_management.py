#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 聊天助手管理示例
演示如何创建、更新、删除、列出聊天助手，以及会话管理和对话功能
"""
from ragflow_sdk import RAGFlow
from typing import List

class ChatAssistantManager:
    def __init__(self, api_key: str, base_url: str):
        """初始化RAGFlow客户端"""
        self.rag_object = RAGFlow(api_key=api_key, base_url=base_url)

    def create_chat_assistant_example(self, name: str, description: str = None):
        """创建聊天助手示例"""
        print("=== 创建聊天助手示例 ===")
        try:
            assistant = self.rag_object.create_chat_assistant(
                name=name,
                description=description or "示例聊天助手"
            )
            print(f"创建聊天助手成功: {assistant.name} (ID: {assistant.id})")
            return assistant
        except Exception as e:
            print(f"创建聊天助手失败: {e}")
            return None

    def list_chat_assistants_example(self):
        """列出聊天助手示例"""
        print("=== 列出聊天助手示例 ===")
        try:
            assistants = self.rag_object.list_chat_assistants()
            for assistant in assistants:
                print(f"  - {assistant.name} (ID: {assistant.id})")
            return assistants
        except Exception as e:
            print(f"列出聊天助手失败: {e}")
            return []

    def update_chat_assistant_example(self, assistant_id: str, new_name: str):
        """更新聊天助手示例"""
        print("=== 更新聊天助手示例 ===")
        try:
            self.rag_object.update_chat_assistant(assistant_id=assistant_id, update_message={"name": new_name})
            print(f"聊天助手(ID: {assistant_id}) 已重命名为: {new_name}")
        except Exception as e:
            print(f"更新聊天助手失败: {e}")

    def delete_chat_assistant_example(self, assistant_id: str):
        """删除聊天助手示例"""
        print("=== 删除聊天助手示例 ===")
        try:
            self.rag_object.delete_chat_assistant(assistant_id=assistant_id)
            print(f"聊天助手(ID: {assistant_id}) 已删除")
        except Exception as e:
            print(f"删除聊天助手失败: {e}")

    def create_session_example(self, assistant_id: str):
        """创建会话示例"""
        print("=== 创建会话示例 ===")
        try:
            session = self.rag_object.create_session_with_chat_assistant(assistant_id=assistant_id)
            print(f"创建会话成功: (ID: {session.id})")
            return session
        except Exception as e:
            print(f"创建会话失败: {e}")
            return None

    def converse_with_assistant_example(self, session_id: str, message: str):
        """与聊天助手对话示例"""
        print("=== 与聊天助手对话示例 ===")
        try:
            response = self.rag_object.converse_with_chat_assistant(session_id=session_id, message=message)
            print(f"助手回复: {response.content}")
        except Exception as e:
            print(f"对话失败: {e}")

    def list_sessions_example(self, assistant_id: str):
        """列出会话示例"""
        print("=== 列出会话示例 ===")
        try:
            sessions = self.rag_object.list_chat_assistant_sessions(assistant_id=assistant_id)
            for session in sessions:
                print(f"  - 会话ID: {session.id}，最后更新时间: {session.update_time}")
            return sessions
        except Exception as e:
            print(f"列出会话失败: {e}")
            return []

    def delete_sessions_example(self, assistant_id: str, session_ids: List[str]):
        """删除会话示例"""
        print("=== 删除会话示例 ===")
        try:
            self.rag_object.delete_chat_assistant_sessions(assistant_id=assistant_id, ids=session_ids)
            print(f"已删除 {len(session_ids)} 个会话")
        except Exception as e:
            print(f"删除会话失败: {e}")


def main():
    """主函数 - 演示聊天助手管理的完整流程"""
    API_KEY = "ragflow-ZjMDYyNDQwNjIxNzExZjBhMjgyMDI0Mm"  # 例如："abc123def456"
    BASE_URL = "http://localhost:9380"  # 例如："http://localhost:9380"
    assistant_name = "测试助手"
    user_message = "你好，能帮我做什么？"

    manager = ChatAssistantManager(API_KEY, BASE_URL)
    # 创建聊天助手
    assistant = manager.create_chat_assistant_example(assistant_name)
    # 列出助手
    assistants = manager.list_chat_assistants_example()
    # 更新助手
    if assistant:
        manager.update_chat_assistant_example(assistant.id, "新助手名")
    # 创建会话
    if assistant:
        session = manager.create_session_example(assistant.id)
        # 对话
        if session:
            manager.converse_with_assistant_example(session.id, user_message)
        # 列出会话
        sessions = manager.list_sessions_example(assistant.id)
        # 删除会话
        if sessions:
            manager.delete_sessions_example(assistant.id, [s.id for s in sessions])
    # 删除助手
    if assistant:
        manager.delete_chat_assistant_example(assistant.id)

if __name__ == "__main__":
    main() 