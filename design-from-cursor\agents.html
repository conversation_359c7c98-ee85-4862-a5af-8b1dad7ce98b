<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理管理 - RAGFlow</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-blue-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="index.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">仪表板</a>
                    <a href="datasets.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">数据集</a>
                    <a href="assistants.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">聊天助手</a>
                    <a href="agents.html" class="text-blue-600 hover:text-blue-800 px-3 py-2 rounded-md text-sm font-medium">代理</a>
                    <a href="api.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">API</a>
                    <a href="settings.html" class="text-gray-600 hover:text-gray-800 px-3 py-2 rounded-md text-sm font-medium">设置</a>
                    <div class="ml-3 relative">
                        <div class="flex items-center space-x-3">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                            <span class="text-sm font-medium text-gray-700">管理员</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 页面标题和操作 -->
        <div class="px-4 py-6 sm:px-0">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">代理管理</h1>
                    <p class="mt-2 text-gray-600">创建和管理智能代理，实现复杂工作流的自动化处理</p>
                </div>
                <button onclick="openCreateModal()" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建代理
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-6 py-4">
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" placeholder="搜索代理..." class="input-modern pl-10 w-full">
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <select class="input-modern">
                            <option>所有状态</option>
                            <option>运行中</option>
                            <option>已停止</option>
                            <option>错误</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 代理列表 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 代理卡片 1 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">数据分析代理</h3>
                                <p class="text-sm text-gray-500">自动分析数据并生成报告</p>
                            </div>
                        </div>
                        <span class="status-indicator status-online"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">状态</span>
                            <span class="tag tag-green">运行中</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">45</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-15</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openAgentSession('data-analysis')" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700">
                            <i class="fas fa-play mr-2"></i>启动会话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 代理卡片 2 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">文档处理代理</h3>
                                <p class="text-sm text-gray-500">自动处理文档和生成摘要</p>
                            </div>
                        </div>
                        <span class="status-indicator status-online"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">状态</span>
                            <span class="tag tag-green">运行中</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">78</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-12</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openAgentSession('doc-processor')" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700">
                            <i class="fas fa-play mr-2"></i>启动会话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 代理卡片 3 -->
            <div class="bg-white shadow rounded-lg card-hover">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-violet-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-xl"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-lg font-medium text-gray-900">代码生成代理</h3>
                                <p class="text-sm text-gray-500">根据需求自动生成代码</p>
                            </div>
                        </div>
                        <span class="status-indicator status-offline"></span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">状态</span>
                            <span class="tag tag-orange">已停止</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">会话数量</span>
                            <span class="text-gray-900">32</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">创建时间</span>
                            <span class="text-gray-900">2024-01-10</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="openAgentSession('code-generator')" class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-purple-700">
                            <i class="fas fa-play mr-2"></i>启动会话
                        </button>
                        <button class="text-gray-600 hover:text-gray-800 px-3 py-2">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="text-red-600 hover:text-red-800 px-3 py-2">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作流画布预览 -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">工作流画布</h3>
                <p class="text-sm text-gray-500">可视化编辑代理的工作流程</p>
            </div>
            <div class="p-6">
                <div class="bg-gray-50 rounded-lg p-8 text-center">
                    <i class="fas fa-project-diagram text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-500">点击代理卡片查看详细的工作流配置</p>
                    <button class="mt-4 btn-primary">
                        <i class="fas fa-edit mr-2"></i>编辑工作流
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建代理模态框 -->
    <div id="createModal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">创建代理</h3>
                <button onclick="closeCreateModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">代理名称</label>
                        <input type="text" class="input-modern w-full" placeholder="输入代理名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                        <textarea class="input-modern w-full" rows="3" placeholder="描述这个代理的功能"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">代理类型</label>
                        <select class="input-modern w-full">
                            <option>数据分析代理</option>
                            <option>文档处理代理</option>
                            <option>代码生成代理</option>
                            <option>自定义代理</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">工作流配置</label>
                        <textarea class="input-modern w-full" rows="6" placeholder="输入DSL工作流配置或使用可视化编辑器"></textarea>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeCreateModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="btn-primary">
                        创建代理
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 代理会话模态框 -->
    <div id="agentSessionModal" class="modal-overlay hidden">
        <div class="modal-content" style="max-width: 900px; height: 80vh;">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">代理会话</h3>
                <button onclick="closeAgentSessionModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="flex flex-col h-full">
                <!-- 会话消息区域 -->
                <div id="agentMessages" class="flex-1 overflow-y-auto p-4 bg-gray-50 rounded-lg mb-4">
                    <div class="message-assistant">
                        <p>代理已启动，请输入您的需求或任务描述。</p>
                    </div>
                </div>
                <!-- 输入区域 -->
                <div class="flex space-x-2">
                    <input type="text" id="agentInput" placeholder="输入任务需求..." class="input-modern flex-1">
                    <button onclick="sendAgentMessage()" class="btn-primary">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openCreateModal() {
            document.getElementById('createModal').classList.remove('hidden');
        }

        function closeCreateModal() {
            document.getElementById('createModal').classList.add('hidden');
        }

        function openAgentSession(agentId) {
            document.getElementById('agentSessionModal').classList.remove('hidden');
            // 这里可以根据agentId加载对应的代理配置
        }

        function closeAgentSessionModal() {
            document.getElementById('agentSessionModal').classList.add('hidden');
        }

        function sendAgentMessage() {
            const input = document.getElementById('agentInput');
            const message = input.value.trim();
            if (!message) return;

            const messagesContainer = document.getElementById('agentMessages');
            
            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'message-user';
            userMessage.innerHTML = `<p>${message}</p>`;
            messagesContainer.appendChild(userMessage);

            // 清空输入框
            input.value = '';

            // 模拟代理处理
            setTimeout(() => {
                const agentMessage = document.createElement('div');
                agentMessage.className = 'message-assistant';
                agentMessage.innerHTML = `<p>正在处理您的请求，请稍候...</p>`;
                messagesContainer.appendChild(agentMessage);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 1000);

            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // 回车发送消息
        document.addEventListener('DOMContentLoaded', function() {
            const agentInput = document.getElementById('agentInput');
            if (agentInput) {
                agentInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendAgentMessage();
                    }
                });
            }
        });
    </script>
</body>
</html> 