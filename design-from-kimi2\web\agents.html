<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - 智能代理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="datasets.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-database mr-2"></i>数据集
                        </a>
                        <a href="chat-assistants.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-robot mr-2"></i>聊天助手
                        </a>
                        <a href="agents.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-magic mr-2"></i>代理
                        </a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-plus mr-2"></i>创建代理
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">智能代理</h1>
            <p class="mt-2 text-gray-600">多步骤任务自动化代理</p>
        </div>

        <!-- Agents Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Agent Card 1 -->
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-search text-purple-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">数据分析师</h3>
                                <p class="text-sm text-gray-600">自动数据洞察</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">活跃</span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-tasks mr-2"></i>
                            <span>3 个步骤</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>平均执行时间: 2分钟</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-chart-line mr-2"></i>
                            <span>156 次执行</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 px-3 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700">
                            <i class="fas fa-play mr-1"></i>运行
                        </button>
                        <a href="agent-config.html" class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200">
                            <i class="fas fa-cog"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Agent Card 2 -->
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-file-export text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">报告生成器</h3>
                                <p class="text-sm text-gray-600">自动生成报告</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">活跃</span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-tasks mr-2"></i>
                            <span>5 个步骤</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>平均执行时间: 5分钟</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-chart-line mr-2"></i>
                            <span>89 次执行</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 px-3 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700">
                            <i class="fas fa-play mr-1"></i>运行
                        </button>
                        <a href="agent-config.html" class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200">
                            <i class="fas fa-cog"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Agent Card 3 -->
            <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-sync text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">数据同步器</h3>
                                <p class="text-sm text-gray-600">定期数据更新</p>
                            </div>
                        </div>
                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">暂停</span>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-tasks mr-2"></i>
                            <span>2 个步骤</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-clock mr-2"></i>
                            <span>频率: 每日</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-chart-line mr-2"></i>
                            <span>1,247 次执行</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 px-3 py-2 bg-gray-300 text-gray-500 text-sm rounded-lg cursor-not-allowed">
                            <i class="fas fa-play mr-1"></i>已暂停
                        </button>
                        <a href="agent-config.html" class="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200">
                            <i class="fas fa-cog"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>