#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 块管理示例
演示如何添加、列出、删除、更新、检索块，以及相似度搜索和向量检索
"""
from ragflow_sdk import RAGFlow
from typing import List
import time

class ChunkManager:
    def __init__(self, api_key: str, base_url: str):
        """初始化RAGFlow客户端"""
        self.rag_object = RAGFlow(api_key=api_key, base_url=base_url)

    def add_chunk_example(self, document_id: str, content: str, important_keywords: List[str] = None):
        """添加块示例"""
        print("=== 添加块示例 ===")
        try:
            # 首先获取数据集
            datasets = self.rag_object.list_datasets()
            if not datasets:
                print("没有找到数据集，请先创建数据集")
                return None
            
            # 获取文档
            documents = datasets[0].list_documents(id=document_id)
            if not documents:
                print(f"没有找到ID为 {document_id} 的文档")
                return None
            
            document = documents[0]
            
            # 检查文档是否已解析完成
            if document.run != "DONE":
                print(f"文档 {document.name} 尚未解析完成，当前状态: {document.run}")
                print("请等待文档解析完成后再添加块")
                return None
            
            # 通过文档对象添加块
            chunk = document.add_chunk(content=content, important_keywords=important_keywords or [])
            print(f"添加块成功: (ID: {chunk.id}) 内容: {chunk.content[:30]}...")
            return chunk
        except Exception as e:
            print(f"添加块失败: {e}")
            return None

    def list_chunks_example(self, document_id: str, keywords: str = None, page: int = 1, page_size: int = 30):
        """列出块示例"""
        print("=== 列出块示例 ===")
        try:
            # 首先获取数据集
            datasets = self.rag_object.list_datasets()
            if not datasets:
                print("没有找到数据集，请先创建数据集")
                return []
            
            # 获取文档
            documents = datasets[0].list_documents(id=document_id)
            if not documents:
                print(f"没有找到ID为 {document_id} 的文档")
                return []
            
            document = documents[0]
            # 通过文档对象列出块
            chunks = document.list_chunks(keywords=keywords, page=page, page_size=page_size)
            for chunk in chunks:
                print(f"  - (ID: {chunk.id}) 内容: {chunk.content[:30]}...")
            return chunks
        except Exception as e:
            print(f"列出块失败: {e}")
            return []

    def update_chunk_example(self, document_id: str, chunk_id: str, new_content: str, important_keywords: List[str] = None):
        """更新块内容示例"""
        print("=== 更新块示例 ===")
        try:
            # 首先获取数据集
            datasets = self.rag_object.list_datasets()
            if not datasets:
                print("没有找到数据集，请先创建数据集")
                return
            
            # 获取文档
            documents = datasets[0].list_documents(id=document_id)
            if not documents:
                print(f"没有找到ID为 {document_id} 的文档")
                return
            
            document = documents[0]
            # 获取块
            chunks = document.list_chunks(id=chunk_id)
            if not chunks:
                print(f"没有找到ID为 {chunk_id} 的块")
                return
            
            chunk = chunks[0]
            # 通过块对象更新内容
            update_message = {"content": new_content}
            if important_keywords is not None:
                update_message["important_keywords"] = important_keywords
            
            chunk.update(update_message)
            print(f"块(ID: {chunk_id}) 内容已更新")
        except Exception as e:
            print(f"更新块失败: {e}")

    def delete_chunk_example(self, document_id: str, chunk_ids: List[str]):
        """删除块示例"""
        print("=== 删除块示例 ===")
        try:
            # 首先获取数据集
            datasets = self.rag_object.list_datasets()
            if not datasets:
                print("没有找到数据集，请先创建数据集")
                return
            
            # 获取文档
            documents = datasets[0].list_documents(id=document_id)
            if not documents:
                print(f"没有找到ID为 {document_id} 的文档")
                return
            
            document = documents[0]
            # 通过文档对象删除块
            document.delete_chunks(chunk_ids=chunk_ids)
            print(f"块(IDs: {chunk_ids}) 已删除")
        except Exception as e:
            print(f"删除块失败: {e}")

    def retrieve_chunks_example(self, dataset_ids: List[str], question: str, document_ids: List[str] = None, 
                               page: int = 1, page_size: int = 30, similarity_threshold: float = 0.2):
        """检索块示例（相似度搜索/向量检索）"""
        print("=== 检索块示例 ===")
        try:
            # 使用RAGFlow的retrieve方法进行检索
            results = self.rag_object.retrieve(
                question=question,
                dataset_ids=dataset_ids,
                document_ids=document_ids,
                page=page,
                page_size=page_size,
                similarity_threshold=similarity_threshold
            )
            print(f"检索到 {len(results)} 个相关块：")
            for chunk in results:
                print(f"  - (ID: {chunk.id}) 相似度: {chunk.similarity:.2f} 内容: {chunk.content[:30]}...")
            return results
        except Exception as e:
            print(f"检索块失败: {e}")
            return []

    def create_dataset_and_document_example(self, dataset_name: str, document_name: str, content: str):
        """创建数据集和文档示例"""
        print("=== 创建数据集和文档示例 ===")
        try:
            # 检查是否已存在同名数据集，如果存在则删除
            existing_datasets = self.rag_object.list_datasets(name=dataset_name)
            if existing_datasets:
                print(f"发现已存在同名数据集 '{dataset_name}'，正在删除...")
                for dataset in existing_datasets:
                    self.rag_object.delete_datasets(ids=[dataset.id])
                    print(f"已删除数据集: {dataset.name} (ID: {dataset.id})")
                # 等待一下确保删除完成
                time.sleep(2)
            
            # 创建数据集
            dataset = self.rag_object.create_dataset(
                name=dataset_name,
                embedding_model="BAAI/bge-large-zh-v1.5@BAAI",
                chunk_method="naive"
            )
            print(f"创建数据集成功: (ID: {dataset.id}) 名称: {dataset.name}")
            
            # 上传文档
            document_list = [{"display_name": document_name, "blob": content.encode('utf-8')}]
            dataset.upload_documents(document_list)
            
            # 获取上传的文档
            documents = dataset.list_documents(keywords=document_name)
            if documents:
                document = documents[0]
                print(f"创建文档成功: (ID: {document.id}) 名称: {document.name}")
                
                # 解析文档
                print("开始解析文档...")
                dataset.async_parse_documents([document.id])
                
                # 等待文档解析完成
                max_wait_time = 60  # 最多等待60秒
                wait_time = 0
                while wait_time < max_wait_time:
                    # 重新获取文档状态
                    documents = dataset.list_documents(id=document.id)
                    if documents:
                        doc = documents[0]
                        print(f"文档解析进度: {doc.progress:.1f}% - 状态: {doc.run}")
                        
                        if doc.run == "DONE":
                            print("文档解析完成！")
                            return dataset, doc
                        elif doc.run == "FAIL":
                            print("文档解析失败！")
                            return dataset, None
                    
                    time.sleep(2)
                    wait_time += 2
                
                print("文档解析超时，但继续执行...")
                return dataset, document
            else:
                print("文档创建失败")
                return dataset, None
        except Exception as e:
            print(f"创建数据集和文档失败: {e}")
            return None, None

def main():
    """主函数 - 演示块管理的完整流程"""
    API_KEY = "ragflow-ZjMDYyNDQwNjIxNzExZjBhMjgyMDI0Mm"  # 例如："abc123def456"
    BASE_URL = "http://localhost:9380"  # 例如："http://localhost:9380"
    DATASET_NAME = "测试数据集"
    
    chunk_manager = ChunkManager(API_KEY, BASE_URL)
    
    try:
        # 创建数据集和文档
        dataset, document = chunk_manager.create_dataset_and_document_example(
            dataset_name=DATASET_NAME,
            document_name="测试文档.txt",
            content="这是一个测试文档的内容。包含一些重要的信息，用于测试块管理功能。"
        )
        
        if dataset and document:
            # 添加块
            chunk = chunk_manager.add_chunk_example(
                document_id=document.id, 
                content="这是一个手动添加的知识块的内容示例。",
                important_keywords=["知识块", "示例", "手动添加"]
            )
            
            # 列出块
            chunks = chunk_manager.list_chunks_example(document_id=document.id)
            
            # 更新块
            if chunk:
                chunk_manager.update_chunk_example(
                    document_id=document.id,
                    chunk_id=chunk.id, 
                    new_content="更新后的知识块内容，包含更多详细信息。",
                    important_keywords=["更新", "知识块", "详细信息"]
                )
            
            # 检索块
            chunk_manager.retrieve_chunks_example(
                dataset_ids=[dataset.id],
                question="知识块检索关键词"
            )
            
            # 删除块
            if chunk:
                chunk_manager.delete_chunk_example(
                    document_id=document.id,
                    chunk_ids=[chunk.id]
                )
        
        print("\n=== 演示完成 ===")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
    
if __name__ == "__main__":
    main() 