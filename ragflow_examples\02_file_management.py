#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAGFlow 文件管理示例
演示如何上传文档到数据集
"""
from ragflow_sdk import RAGFlow
from typing import List
import os

class FileManager:
    def __init__(self, api_key: str, base_url: str):
        """初始化RAGFlow客户端"""
        self.api_key = api_key
        self.base_url = base_url
        self.rag_object = RAGFlow(api_key=api_key, base_url=base_url)

    def upload_documents_example(self, dataset_id: str, file_path: str):
        """上传文档示例"""
        print(f"=== 上传文档示例 ===")
        try:
            # 首先获取数据集对象
            datasets = self.rag_object.list_datasets(id=dataset_id)
            if not datasets:
                print(f"未找到ID为 {dataset_id} 的数据集")
                return None
            
            dataset = datasets[0]
            
            # 检查是否已存在相同文件名的文档
            file_name = os.path.basename(file_path)
            existing_documents = self.list_documents_example(dataset_id)
            
            if existing_documents:
                for doc in existing_documents:
                    if isinstance(doc, dict):
                        doc_name = doc.get('name', '')
                        if doc_name == file_name:
                            print(f"文档 '{file_name}' 已存在，跳过上传")
                            return {"name": file_name, "id": doc.get('id'), "exists": True}
            
            # 读取文件内容
            with open(file_path, 'rb') as f:
                file_content = f.read()
            
            # 使用DataSet对象的upload_documents方法
            dataset.upload_documents([
                {
                    "display_name": file_name,
                    "blob": file_content
                }
            ])
            
            print(f"上传文档成功: {file_name}")
            return {"name": file_name, "id": "uploaded", "exists": False}
        except Exception as e:
            print(f"上传文档失败: {e}")
            return None

    def update_document_example(self, document_id: str, new_name: str):
        """更新文档信息示例"""
        print(f"=== 更新文档示例 ===")
        try:
            # 根据官方文档，更新文档需要先获取文档对象
            # 这里使用HTTP API直接更新
            import requests
            
            url = f"{self.base_url}/api/v1/documents/{document_id}"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            data = {"name": new_name}
            
            response = requests.put(url, headers=headers, json=data)
            if response.status_code == 200:
                print(f"文档重命名为: {new_name}")
            else:
                print(f"更新文档失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"更新文档失败: {e}")

    def download_document_example(self, document_id: str, save_path: str, dataset_id: str = None):
        """下载文档示例 - 使用官方推荐的方式"""
        print(f"=== 下载文档示例 ===")
        try:
            if not dataset_id:
                print("需要提供数据集ID才能下载文档")
                return
            
            # 使用官方推荐的方式下载文档
            # 1. 获取数据集
            datasets = self.rag_object.list_datasets(id=dataset_id)
            if not datasets:
                print(f"未找到ID为 {dataset_id} 的数据集")
                return
            
            dataset = datasets[0]
            print(f"找到数据集: {dataset.name}")
            
            # 2. 获取文档对象
            documents = dataset.list_documents(id=document_id)
            if not documents:
                print(f"未找到ID为 {document_id} 的文档")
                return
            
            doc = documents[0]
            print(f"找到文档: {doc.name}")
            
            # 3. 下载文档内容
            document_content = doc.download()
            if document_content:
                # 保存文档内容
                with open(save_path, 'wb') as f:
                    f.write(document_content)
                print(f"文档已成功保存到: {save_path}")
            else:
                print("下载的文档内容为空")
            
        except Exception as e:
            print(f"下载文档失败: {e}")
            import traceback
            traceback.print_exc()

    def list_documents_example(self, dataset_id: str):
        """列出文档示例"""
        print(f"=== 列出文档示例 ===")
        try:
            # 使用HTTP API列出文档
            import requests
            
            url = f"{self.base_url}/api/v1/datasets/{dataset_id}/documents"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                data = response.json()
                
                documents = data.get('data', [])
                if isinstance(documents, dict):
                    # 如果data是字典，可能包含docs字段
                    docs_list = documents.get('docs', [])
                    print(f"找到 {len(docs_list)} 个文档:")
                    for doc in docs_list:
                        if isinstance(doc, dict):
                            print(f"  - {doc.get('name', 'Unknown')} (ID: {doc.get('id', 'Unknown')})")
                        else:
                            print(f"  - {doc}")
                    return docs_list
                else:
                    print(f"找到 {len(documents)} 个文档:")
                    for doc in documents:
                        if isinstance(doc, dict):
                            print(f"  - {doc.get('name', 'Unknown')} (ID: {doc.get('id', 'Unknown')})")
                        else:
                            print(f"  - {doc}")
                    return documents
            else:
                print(f"列出文档失败，状态码: {response.status_code}")
                return []
        except Exception as e:
            print(f"列出文档失败: {e}")
            return []

    def delete_documents_example(self, document_ids: List[str]):
        """删除文档示例"""
        print(f"=== 删除文档示例 ===")
        try:
            # 使用HTTP API删除文档
            import requests
            
            url = f"{self.base_url}/api/v1/documents"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            data = {"ids": document_ids}
            
            response = requests.delete(url, headers=headers, json=data)
            if response.status_code == 200:
                print(f"文档(IDs: {document_ids}) 已删除")
            else:
                print(f"删除文档失败，状态码: {response.status_code}")
        except Exception as e:
            print(f"删除文档失败: {e}")

    def parse_document_example(self, document_id: str, dataset_id: str = None):
        """解析文档示例 - 使用官方推荐的方式"""
        print(f"=== 解析文档示例 ===")
        try:
            if not dataset_id:
                print("需要提供数据集ID才能解析文档")
                return
            
            # 使用官方推荐的方式解析文档
            # 1. 获取数据集
            datasets = self.rag_object.list_datasets(id=dataset_id)
            if not datasets:
                print(f"未找到ID为 {dataset_id} 的数据集")
                return
            
            dataset = datasets[0]
            print(f"找到数据集: {dataset.name}")
            
            # 2. 使用数据集的async_parse_documents方法解析文档
            dataset.async_parse_documents([document_id])
            print(f"文档(ID: {document_id}) 异步解析已启动")
            print("异步批量解析已启动。")
            
        except Exception as e:
            print(f"解析文档失败: {e}")
            import traceback
            traceback.print_exc()

    def stop_parsing_document_example(self, document_id: str, dataset_id: str = None):
        """停止解析文档示例 - 使用官方推荐的方式"""
        print(f"=== 停止解析文档示例 ===")
        try:
            if not dataset_id:
                print("需要提供数据集ID才能停止解析文档")
                return
            
            # 使用官方推荐的方式停止解析文档
            # 1. 获取数据集
            datasets = self.rag_object.list_datasets(id=dataset_id)
            if not datasets:
                print(f"未找到ID为 {dataset_id} 的数据集")
                return
            
            dataset = datasets[0]
            print(f"找到数据集: {dataset.name}")
            
            # 2. 使用数据集的async_cancel_parse_documents方法停止解析
            dataset.async_cancel_parse_documents([document_id])
            print(f"文档(ID: {document_id}) 异步解析已停止")
            print("异步批量解析已取消。")
            
        except Exception as e:
            print(f"停止解析失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数 - 演示文件管理的完整流程"""
    API_KEY = "ragflow-ZjMDYyNDQwNjIxNzExZjBhMjgyMDI0Mm"  # 例如："abc123def456"
    BASE_URL = "http://localhost:9380"  # 例如："http://localhost:9380"
    DATASET_ID = "f6fe2d8062b211f08a7e0242ac140002"  # 需替换为实际数据集ID
    FILE_PATH = "test.txt"  # 需替换为实际文件路径
    SAVE_PATH = "downloaded_file.txt"  # 下载保存路径

    # 创建测试文件
    with open(FILE_PATH, "w", encoding="utf-8") as f:
        f.write("这是一个测试文档。\n\nRAGFlow是一个开源的RAG（检索增强生成）框架。")

    file_manager = FileManager(API_KEY, BASE_URL)
    
    # 1. 上传文档
    doc = file_manager.upload_documents_example(DATASET_ID, FILE_PATH)
    if doc:
        if doc.get('exists', False):
            print(f"文档 '{doc['name']}' 已存在，使用现有文档")
            doc_id = doc['id']
        else:
            print(f"文档上传成功: {doc['name']}")
            # 如果刚上传成功，需要重新获取文档ID
            documents = file_manager.list_documents_example(DATASET_ID)
            doc_id = None
            if documents:
                for doc_item in documents:
                    if isinstance(doc_item, dict):
                        doc_name = doc_item.get('name', '')
                        if doc_name == 'test.txt':
                            doc_id = doc_item.get('id')
                            break
        
        if doc_id:
            
            # # 3. 更新文档
            # file_manager.update_document_example(doc_id, "新文档名.txt")
            
            # # 4. 解析文档
            #file_manager.parse_document_example(doc_id, DATASET_ID)
            
            # # 5. 停止解析
            # file_manager.stop_parsing_document_example(doc_id, DATASET_ID)
            
            # 6. 下载文档
             print(f"准备下载文档: test.txt (ID: {doc_id})")
             file_manager.download_document_example(doc_id, SAVE_PATH, DATASET_ID)
            
            # # 7. 删除文档
            # file_manager.delete_documents_example([doc_id])
        else:
            print("无法获取文档ID，跳过下载操作")
    else:
        print("文档上传失败")

if __name__ == "__main__":
    main() 