/* 自定义样式 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 卡片悬停效果 */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* 输入框样式 */
.input-modern {
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.input-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* 加载动画 */
.loading-spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态指示器 */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online {
    background-color: #10b981;
}

.status-offline {
    background-color: #ef4444;
}

.status-warning {
    background-color: #f59e0b;
}

/* 表格样式 */
.table-modern {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table-modern th {
    background-color: #f9fafb;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

.table-modern td {
    padding: 12px 16px;
    border-bottom: 1px solid #f3f4f6;
}

.table-modern tr:hover {
    background-color: #f9fafb;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 0.75rem;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.tag-blue {
    background-color: #dbeafe;
    color: #1e40af;
}

.tag-green {
    background-color: #d1fae5;
    color: #065f46;
}

.tag-purple {
    background-color: #e9d5ff;
    color: #7c3aed;
}

.tag-orange {
    background-color: #fed7aa;
    color: #c2410c;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

/* 聊天消息样式 */
.message-user {
    background-color: #667eea;
    color: white;
    border-radius: 1rem 1rem 0.25rem 1rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    max-width: 70%;
    margin-left: auto;
}

.message-assistant {
    background-color: #f3f4f6;
    color: #374151;
    border-radius: 1rem 1rem 1rem 0.25rem;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    max-width: 70%;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8fafc;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #eff6ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .grid-cols-1 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    
    .modal-content {
        margin: 1rem;
        padding: 1rem;
    }
    
    .message-user,
    .message-assistant {
        max-width: 85%;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
} 