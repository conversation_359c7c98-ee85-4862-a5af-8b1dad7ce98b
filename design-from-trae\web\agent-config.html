<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAGFlow - 代理配置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-brain text-2xl text-indigo-600 mr-3"></i>
                        <span class="text-xl font-bold text-gray-900">RAGFlow</span>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="dashboard.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                        </a>
                        <a href="datasets.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-database mr-2"></i>数据集
                        </a>
                        <a href="chat-assistants.html" class="text-gray-500 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium">
                            <i class="fas fa-robot mr-2"></i>聊天助手
                        </a>
                        <a href="agents.html" class="text-indigo-600 border-indigo-500 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            <i class="fas fa-magic mr-2"></i>代理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">数据分析师配置</h1>
                    <p class="mt-2 text-gray-600">配置代理参数和工作流程</p>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>保存配置
                    </button>
                </div>
            </div>
        </div>

        <!-- Configuration Form -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="space-y-6">
                <!-- Basic Settings -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">基本设置</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">代理名称</label>
                            <input type="text" value="数据分析师" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">执行频率</label>
                            <select class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500">
                                <option>手动触发</option>
                                <option>每小时</option>
                                <option>每日</option>
                                <option>每周</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Data Sources -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">数据源配置</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" checked class="mr-3">
                            <label class="text-sm">技术文档数据集</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" checked class="mr-3">
                            <label class="text-sm">用户行为日志</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="mr-3">
                            <label class="text-sm">性能监控数据</label>
                        </div>
                    </div>
                </div>

                <!-- Workflow Steps -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">工作流程</h3>
                    <div class="space-y-3">
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm mr-3">1</span>
                                    <span class="font-medium">数据收集</span>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm mr-3">2</span>
                                    <span class="font-medium">数据分析</span>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <span class="w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm mr-3">3</span>
                                    <span class="font-medium">生成报告</span>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-cog"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">通知设置</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" checked class="mr-3">
                            <label class="text-sm">执行完成邮件通知</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" checked class="mr-3">
                            <label class="text-sm">错误时发送警报</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>